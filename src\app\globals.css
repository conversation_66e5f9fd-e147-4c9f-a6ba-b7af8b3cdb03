@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Revantad Store Brand Colors - Green and Mustard */
  --primary-green: #22c55e;
  --primary-green-dark: #16a34a;
  --secondary-mustard: #facc15;
  --secondary-mustard-dark: #eab308;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --primary-green: #15803d;
    --secondary-mustard: #a16207;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

html {
  scroll-behavior: smooth;
}

/* Revantad Store Custom Component Classes */
.btn-primary {
  @apply bg-green-500 hover:bg-green-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.btn-secondary {
  @apply bg-yellow-400 hover:bg-yellow-500 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.btn-outline {
  @apply border-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white font-medium px-6 py-3 rounded-lg transition-all duration-200;
}

.card {
  @apply bg-white dark:bg-slate-800 rounded-xl shadow-md border border-gray-100 dark:border-slate-700;
}

.hero-gradient {
  @apply bg-gradient-to-br from-green-500 via-green-600 to-yellow-400;
}

.text-gradient {
  @apply bg-gradient-to-r from-green-600 to-yellow-500 bg-clip-text text-transparent;
}

.glass-effect {
  @apply backdrop-blur-md bg-white/10 border border-white/20;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.6);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.4);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.6);
}

/* Sidebar specific scrollbar */
.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3);
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.5);
}

/* Prevent text blurring on transforms */
.sidebar-nav-item {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Ensure crisp text rendering */
.crisp-text {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Enhanced backdrop blur for sticky elements */
.backdrop-blur-enhanced {
  backdrop-filter: blur(8px) saturate(180%);
  -webkit-backdrop-filter: blur(8px) saturate(180%);
}

/* Smooth scrolling for sidebar navigation */
.sidebar-nav-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
}

/* Enhanced scrollbar for navigation area - Professional & Larger */
.sidebar-nav-scroll {
  scrollbar-width: auto;
  scrollbar-color: rgba(34, 197, 94, 0.5) rgba(243, 244, 246, 0.4);
  overflow-y: auto !important;
  overflow-x: hidden;
}

.sidebar-nav-scroll::-webkit-scrollbar {
  width: 14px;
  background: rgba(243, 244, 246, 0.5);
  border-radius: 7px;
  display: block !important;
}

.sidebar-nav-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.5);
  border-radius: 7px;
  margin: 2px 0;
  border: 1px solid rgba(229, 231, 235, 0.6);
  min-height: 50px;
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.7) 0%, rgba(16, 185, 129, 0.6) 100%);
  border-radius: 7px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(34, 197, 94, 0.3);
  min-height: 30px;
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(16, 185, 129, 0.7) 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.3);
  transform: scale(1.05);
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.4);
}

/* Dark theme enhanced scrollbar */
.dark .sidebar-nav-scroll {
  scrollbar-color: rgba(34, 197, 94, 0.5) rgba(51, 65, 85, 0.4);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar {
  background: rgba(51, 65, 85, 0.6);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.6);
  border: 1px solid rgba(71, 85, 105, 0.7);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.7) 0%, rgba(16, 185, 129, 0.6) 100%);
  border: 2px solid rgba(30, 41, 59, 0.3);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
  border-color: rgba(30, 41, 59, 0.4);
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.4);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(34, 197, 94, 1) 0%, rgba(16, 185, 129, 0.9) 100%);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.5);
}

/* Professional gradient overlays */
.gradient-overlay-top {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), transparent);
  pointer-events: none;
}

.dark .gradient-overlay-top {
  background: linear-gradient(to bottom, rgba(30, 41, 59, 0.9), transparent);
}

.gradient-overlay-bottom {
  background: linear-gradient(to top, rgba(249, 250, 251, 0.9), transparent);
  pointer-events: none;
}

.dark .gradient-overlay-bottom {
  background: linear-gradient(to top, rgba(15, 23, 42, 0.9), transparent);
}

/* Professional Main Content Scrollbar */
.main-content-scroll {
  scroll-behavior: smooth;
  scrollbar-width: auto;
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(243, 244, 246, 0.4);
}

.main-content-scroll::-webkit-scrollbar {
  width: 12px;
  background: rgba(243, 244, 246, 0.3);
  border-radius: 6px;
}

.main-content-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.3);
  border-radius: 6px;
  margin: 4px 0;
  border: 1px solid rgba(229, 231, 235, 0.4);
}

.main-content-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6) 0%, rgba(37, 99, 235, 0.5) 100%);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  min-height: 30px;
}

.main-content-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.7) 0%, rgba(37, 99, 235, 0.6) 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
  transform: scale(1.02);
}

.main-content-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(37, 99, 235, 0.7) 100%);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.4);
}

/* Dark theme for main content scrollbar */
.dark .main-content-scroll {
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(51, 65, 85, 0.4);
}

.dark .main-content-scroll::-webkit-scrollbar {
  background: rgba(51, 65, 85, 0.4);
}

.dark .main-content-scroll::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.5);
}

.dark .main-content-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6) 0%, rgba(37, 99, 235, 0.5) 100%);
  border: 1px solid rgba(30, 41, 59, 0.3);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.dark .main-content-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(37, 99, 235, 0.7) 100%);
  border-color: rgba(30, 41, 59, 0.4);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.4);
}

.dark .main-content-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.8) 100%);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.5);
}

/* Fade indicators for scrollable content */
.scroll-fade-top::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
  pointer-events: none;
  z-index: 5;
}

.dark .scroll-fade-top::before {
  background: linear-gradient(to bottom, rgba(30, 41, 59, 0.8), transparent);
}

.scroll-fade-bottom::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(249, 250, 251, 0.8), transparent);
  pointer-events: none;
  z-index: 5;
}

.dark .scroll-fade-bottom::after {
  background: linear-gradient(to top, rgba(15, 23, 42, 0.8), transparent);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800;
}

/* Hover effects */
.hover-lift {
  @apply transition-transform duration-200 hover:-translate-y-1;
}

.hover-glow {
  @apply transition-shadow duration-200 hover:shadow-lg hover:shadow-green-500/25;
}
























